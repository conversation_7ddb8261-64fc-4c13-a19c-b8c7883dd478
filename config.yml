# Telegram application credentials (получаются на https://my.telegram.org/apps)
api_id: 20777053
api_hash: "75bd3873dc6a9866f0b2c9a9492b4c29"

# Номер телефона пользователя (user-бота)
phone: "+447418354742"

# Токен Bot API-бота, который будет посылать «buy …»
bot_token: "6927712625:AAFTFGG-50l1UOusQ4HiDDjMR6m5UjGSM48"

# Канал, который слушаем (обязательно c префиксом -100)
listen_channel_id: 1998961899   # GemTools alerts

# Чат или канал, куда пересылаем сигнал
target_chat_id:  -1002068844431    # Личный альфа-чат
enable_logging: true
filters: []  # Пустой массив = обрабатывать все сообщения
# filters: ["PumpFun testing phase:", "New token"]  # Пример с фильтрами