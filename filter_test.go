package main

import (
	"strings"
	"testing"
)

// Функция для тестирования логики фильтрации
func checkFilters(text string, filters []string) bool {
	if len(filters) == 0 {
		return true // Если фильтры не заданы - обрабатываем все сообщения
	}
	
	// Если фильтры заданы - проверяем каждый, достаточно одного совпадения
	for _, filter := range filters {
		if strings.Contains(text, filter) {
			return true
		}
	}
	return false
}

func TestFilterLogic(t *testing.T) {
	tests := []struct {
		name     string
		text     string
		filters  []string
		expected bool
	}{
		{
			name:     "Пустой массив фильтров - должен пропускать все",
			text:     "Любое сообщение",
			filters:  []string{},
			expected: true,
		},
		{
			name:     "Один фильтр - совпадение",
			text:     "PumpFun testing phase: новый токен",
			filters:  []string{"PumpFun testing phase:"},
			expected: true,
		},
		{
			name:     "Один фильтр - нет совпадения",
			text:     "Обычное сообщение",
			filters:  []string{"PumpFun testing phase:"},
			expected: false,
		},
		{
			name:     "Несколько фильтров - первый совпадает",
			text:     "PumpFun testing phase: новый токен",
			filters:  []string{"PumpFun testing phase:", "New token", "Alert"},
			expected: true,
		},
		{
			name:     "Несколько фильтров - второй совпадает",
			text:     "New token detected on chain",
			filters:  []string{"PumpFun testing phase:", "New token", "Alert"},
			expected: true,
		},
		{
			name:     "Несколько фильтров - третий совпадает",
			text:     "Alert: важное уведомление",
			filters:  []string{"PumpFun testing phase:", "New token", "Alert"},
			expected: true,
		},
		{
			name:     "Несколько фильтров - ни один не совпадает",
			text:     "Обычное сообщение без ключевых слов",
			filters:  []string{"PumpFun testing phase:", "New token", "Alert"},
			expected: false,
		},
		{
			name:     "Частичное совпадение в середине текста",
			text:     "Сегодня был запущен New token на платформе",
			filters:  []string{"New token"},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkFilters(tt.text, tt.filters)
			if result != tt.expected {
				t.Errorf("checkFilters(%q, %v) = %v, ожидалось %v", 
					tt.text, tt.filters, result, tt.expected)
			}
		})
	}
}
