package main

import (
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"

	"github.com/amarnathcjd/gogram/telegram"
	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"gopkg.in/yaml.v3"
)

/* ---------- конфигурация ---------- */
type Config struct {
	APIID           int      `yaml:"api_id"`
	APIHash         string   `yaml:"api_hash"`
	Phone           string   `yaml:"phone"`
	BotToken        string   `yaml:"bot_token"`
	ListenChannelID int64    `yaml:"listen_channel_id"`
	TargetChatID    int64    `yaml:"target_chat_id"`
	Filters         []string `yaml:"filters"`        // Фильтры сообщений (если пустой массив - без фильтра)
	EnableLogging   bool     `yaml:"enable_logging"` // Включить подробное логирование
}

func loadConfig(path string) (*Config, error) {
	raw, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var c Config
	if err := yaml.Unmarshal(raw, &c); err != nil {
		return nil, err
	}
	return &c, nil
}

func main() {
	cfg, err := loadConfig("config.yml")
	if err != nil {
		log.Fatalf("❌ Ошибка загрузки конфигурации: %v", err)
	}

	log.Printf("🚀 Запуск Telegram парсера (gogram версия)...")
	log.Printf("📱 Телефон: %s", cfg.Phone)
	log.Printf("📺 Канал для прослушивания: %d", cfg.ListenChannelID)
	log.Printf("🎯 Целевой чат: %d", cfg.TargetChatID)
	if cfg.Filter != "" {
		log.Printf("🔍 Фильтр сообщений: '%s'", cfg.Filter)
	} else {
		log.Printf("🔍 Фильтр сообщений: отключен (обрабатываются все сообщения)")
	}
	log.Printf("📝 Подробное логирование: %t", cfg.EnableLogging)

	/* Bot API для отправки buy сигналов */
	bot, err := tgbotapi.NewBotAPI(cfg.BotToken)
	if err != nil {
		log.Fatalf("❌ Ошибка создания Bot API: %v", err)
	}
	log.Printf("🤖 Bot API инициализирован: @%s", bot.Self.UserName)

	/* Gogram клиент */
	client, err := telegram.NewClient(telegram.ClientConfig{
		AppID:   int32(cfg.APIID),
		AppHash: cfg.APIHash,
	})
	if err != nil {
		log.Fatalf("❌ Ошибка создания gogram клиента: %v", err)
	}

	log.Printf("🔗 Gogram клиент создан")

	// Подключаемся к Telegram
	err = client.Connect()
	if err != nil {
		log.Fatalf("❌ Ошибка подключения: %v", err)
	}
	log.Printf("🔗 Подключение к Telegram установлено")

	// Авторизация
	_, err = client.Login(cfg.Phone)
	if err != nil {
		log.Fatalf("❌ Ошибка авторизации: %v", err)
	}
	log.Printf("✅ Авторизация успешна!")

	// Получаем информацию о канале
	channel, err := client.GetChannel(cfg.ListenChannelID)
	if err != nil {
		log.Fatalf("❌ Не удалось получить канал %d: %v", cfg.ListenChannelID, err)
	}
	log.Printf("📺 Канал найден: %s (ID: %d)", channel.Title, channel.ID)

	// Настраиваем обработчик сообщений из каналов
	client.On(telegram.OnMessage, func(message *telegram.NewMessage) error {
		// Проверяем, что сообщение из нужного канала
		if message.Chat.ID != channel.ID {
			if cfg.EnableLogging {
				log.Printf("⏭️ Сообщение из другого канала %d, пропускаем", message.Chat.ID)
			}
			return nil
		}

		text := message.Text()
		if cfg.EnableLogging {
			log.Printf("📨 Получено сообщение из канала [%s]:", message.Chat.Title)
			log.Printf("═══════════════════════════════════════")
			log.Printf("%s", text)
			log.Printf("═══════════════════════════════════════")
		}

		// Проверяем фильтр
		shouldProcess := false
		if cfg.Filter == "" {
			// Если фильтр пустой - обрабатываем все сообщения
			shouldProcess = true
			if cfg.EnableLogging {
				log.Printf("🔍 Фильтр не задан, обрабатываем все сообщения")
			}
		} else {
			// Если фильтр задан - проверяем его
			shouldProcess = strings.Contains(text, cfg.Filter)
			if cfg.EnableLogging {
				if shouldProcess {
					log.Printf("✅ Сообщение прошло фильтр '%s'", cfg.Filter)
				} else {
					log.Printf("❌ Сообщение не прошло фильтр '%s'", cfg.Filter)
				}
			}
		}

		if shouldProcess {
			// Ищем контракт в сообщении
			re := regexp.MustCompile(`[1-9A-HJ-NP-Za-km-z]{32,44}pump`)
			if contract := re.FindString(text); contract != "" {
				buyMessage := fmt.Sprintf("buy %s", contract)
				_, err := bot.Send(tgbotapi.NewMessage(cfg.TargetChatID, buyMessage))
				if err != nil {
					log.Printf("❌ Ошибка отправки сообщения: %v", err)
				} else {
					log.Printf("🚀 Buy signal sent: %s", buyMessage)
				}
			} else {
				/*				buyMessage := fmt.Sprintf("buy %s", "xxx")
								_, err = bot.Send(tgbotapi.NewMessage(cfg.TargetChatID, buyMessage))
				*/if cfg.EnableLogging {
					log.Printf("🔍 Контракт не найден в сообщении")
				}
			}
		}

		return nil
	}, telegram.FilterChannel) // Фильтруем только сообщения из каналов

	log.Printf("🎯 Парсер запущен и готов к работе!")
	log.Printf("👂 Прослушиваем канал: %s", channel.Title)
	log.Printf("🔄 Для остановки нажмите Ctrl+C")

	// Блокируем main goroutine
	client.Idle()
}
