package main

import (
	"os"
	"testing"
)

func TestConfigLoading(t *testing.T) {
	// Создаем временный файл конфигурации для тестирования
	testConfig := `
api_id: 12345678
api_hash: "test_hash"
phone: "+1234567890"
bot_token: "test_token"
listen_channel_id: -1001234567890
target_chat_id: -1001234567890
filters: ["PumpFun testing phase:", "New token", "Alert"]
enable_logging: true
`

	// Записываем во временный файл
	tmpFile, err := os.CreateTemp("", "test_config_*.yml")
	if err != nil {
		t.Fatalf("Не удалось создать временный файл: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(testConfig); err != nil {
		t.Fatalf("Не удалось записать в временный файл: %v", err)
	}
	tmpFile.Close()

	// Загружаем конфигурацию
	cfg, err := loadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Ошибка загрузки конфигурации: %v", err)
	}

	// Проверяем, что фильтры загрузились правильно
	expectedFilters := []string{"PumpFun testing phase:", "New token", "Alert"}
	if len(cfg.Filters) != len(expectedFilters) {
		t.Errorf("Ожидалось %d фильтров, получено %d", len(expectedFilters), len(cfg.Filters))
	}

	for i, expected := range expectedFilters {
		if i >= len(cfg.Filters) || cfg.Filters[i] != expected {
			t.Errorf("Фильтр %d: ожидался '%s', получен '%s'", i, expected, cfg.Filters[i])
		}
	}

	// Проверяем другие поля
	if cfg.APIID != 12345678 {
		t.Errorf("Ожидался APIID 12345678, получен %d", cfg.APIID)
	}
	if cfg.EnableLogging != true {
		t.Errorf("Ожидался EnableLogging true, получен %t", cfg.EnableLogging)
	}
}

func TestEmptyFiltersConfig(t *testing.T) {
	// Тестируем конфигурацию с пустым массивом фильтров
	testConfig := `
api_id: 12345678
api_hash: "test_hash"
phone: "+1234567890"
bot_token: "test_token"
listen_channel_id: -1001234567890
target_chat_id: -1001234567890
filters: []
enable_logging: false
`

	tmpFile, err := os.CreateTemp("", "test_config_empty_*.yml")
	if err != nil {
		t.Fatalf("Не удалось создать временный файл: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(testConfig); err != nil {
		t.Fatalf("Не удалось записать в временный файл: %v", err)
	}
	tmpFile.Close()

	cfg, err := loadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Ошибка загрузки конфигурации: %v", err)
	}

	// Проверяем, что массив фильтров пустой
	if len(cfg.Filters) != 0 {
		t.Errorf("Ожидался пустой массив фильтров, получено %d элементов", len(cfg.Filters))
	}
}
